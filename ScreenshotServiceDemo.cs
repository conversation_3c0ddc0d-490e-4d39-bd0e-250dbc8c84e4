using System.Runtime.InteropServices;
using Cowpilot.Services;
using Microsoft.Extensions.Logging;

namespace Cowpilot
{
    /// <summary>
    /// Demo application to test and compare different screenshot services
    /// </summary>
    public class ScreenshotServiceDemo
    {
        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string? lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        public static async Task Main(string[] args)
        {
            Console.WriteLine("Screenshot Service Demo");
            Console.WriteLine("=======================");

            var loggerFactory = LoggerFactory.Create(builder => 
                builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            
            var tester = new ScreenshotServiceTester(loggerFactory.CreateLogger<ScreenshotServiceTester>());

            if (args.Length > 0)
            {
                // Test with specific window title
                var windowTitle = string.Join(" ", args);
                Console.WriteLine($"Testing with window title: '{windowTitle}'");
                var results = await tester.TestWithWindowTitleAsync(windowTitle);
                tester.PrintResults(results);
            }
            else
            {
                // Interactive mode
                await RunInteractiveMode(tester);
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        private static async Task RunInteractiveMode(ScreenshotServiceTester tester)
        {
            while (true)
            {
                Console.WriteLine("\nChoose an option:");
                Console.WriteLine("1. Test with foreground window");
                Console.WriteLine("2. Test with specific window title");
                Console.WriteLine("3. Test with common applications");
                Console.WriteLine("4. Exit");
                Console.Write("Enter choice (1-4): ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await TestForegroundWindow(tester);
                        break;
                    case "2":
                        await TestSpecificWindow(tester);
                        break;
                    case "3":
                        await TestCommonApplications(tester);
                        break;
                    case "4":
                        return;
                    default:
                        Console.WriteLine("Invalid choice. Please try again.");
                        break;
                }
            }
        }

        private static async Task TestForegroundWindow(ScreenshotServiceTester tester)
        {
            Console.WriteLine("\nTesting with current foreground window...");
            Console.WriteLine("Switch to the window you want to test and press Enter...");
            Console.ReadLine();

            var results = await tester.TestAllServicesAsync();
            tester.PrintResults(results);
        }

        private static async Task TestSpecificWindow(ScreenshotServiceTester tester)
        {
            Console.Write("\nEnter window title to test: ");
            var windowTitle = Console.ReadLine();

            if (string.IsNullOrWhiteSpace(windowTitle))
            {
                Console.WriteLine("Invalid window title.");
                return;
            }

            var results = await tester.TestWithWindowTitleAsync(windowTitle);
            tester.PrintResults(results);
        }

        private static async Task TestCommonApplications(ScreenshotServiceTester tester)
        {
            var commonWindows = new[]
            {
                "Notepad",
                "Calculator",
                "Microsoft Edge",
                "Google Chrome",
                "Firefox",
                "Visual Studio",
                "Visual Studio Code",
                "Windows Explorer",
                "Command Prompt",
                "PowerShell"
            };

            Console.WriteLine("\nTesting common applications...");

            foreach (var windowTitle in commonWindows)
            {
                var hWnd = FindWindow(null, windowTitle);
                if (hWnd != IntPtr.Zero)
                {
                    Console.WriteLine($"\nFound window: {windowTitle}");
                    var results = await tester.TestAllServicesAsync(hWnd);
                    tester.PrintResults(results);
                    
                    Console.WriteLine("Press Enter to continue to next window...");
                    Console.ReadLine();
                }
                else
                {
                    Console.WriteLine($"Window '{windowTitle}' not found.");
                }
            }
        }
    }
}
