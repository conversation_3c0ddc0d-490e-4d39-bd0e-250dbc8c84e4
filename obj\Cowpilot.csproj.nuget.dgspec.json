{"format": 1, "restore": {"D:\\Projects\\Code\\Cowpilot\\Cowpilot.csproj": {}}, "projects": {"D:\\Projects\\Code\\Cowpilot\\Cowpilot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\Code\\Cowpilot\\Cowpilot.csproj", "projectName": "Cowpilot", "projectPath": "D:\\Projects\\Code\\Cowpilot\\Cowpilot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\Code\\Cowpilot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows10.0.22621.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows10.0.22621": {"targetAlias": "net9.0-windows10.0.22621.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows10.0.22621": {"targetAlias": "net9.0-windows10.0.22621.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Toolkit.Uwp.Notifications": {"target": "Package", "version": "[7.1.3, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCvSharp4.WpfExtensions": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.11.0.20250507, )"}, "Sdcb.PaddleInference.runtime.win64.cu120-sm86-89": {"target": "Package", "version": "[2.6.1, )"}, "Sdcb.PaddleOCR": {"target": "Package", "version": "[2.7.0.3, )"}, "Sdcb.PaddleOCR.Models.Local": {"target": "Package", "version": "[2.7.0, )"}, "Sdcb.PaddleOCR.Models.LocalV4": {"target": "Package", "version": "[2.7.0.1, )"}, "Sdcb.PaddleOCR.Models.Shared": {"target": "Package", "version": "[2.7.0.1, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.22621.57, 10.0.22621.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}