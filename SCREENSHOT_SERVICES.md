# Screenshot Services Documentation

This document describes the different screenshot services implemented in the Cowpilot project, each using different Windows APIs and techniques for capturing window screenshots.

## Overview

All screenshot services implement the `IScreenshotService` interface with a single method:
```csharp
BitmapSource? CaptureWindow(nint hWnd);
```

## Available Services

### 1. BitBltScreenshotService (Original)
**File:** `Services/BitBltScreenshotService.cs`

**Description:** Uses the traditional GDI32 BitBlt API for window capture.

**Advantages:**
- Fast and reliable
- Works with most applications
- Low memory usage
- Compatible with older Windows versions

**Disadvantages:**
- Cannot capture minimized windows
- May fail with hardware-accelerated applications
- Requires window to be visible and unobscured

**Best for:** General-purpose screenshot capture of visible windows

### 2. PrintWindowScreenshotService
**File:** `Services/PrintWindowScreenshotService.cs`

**Description:** Uses the PrintWindow API which can capture window content even when partially obscured.

**Advantages:**
- Can capture partially obscured windows
- Works with minimized windows (sometimes)
- Better compatibility with modern applications
- Uses `PW_RENDERFULLCONTENT` flag for complete content

**Disadvantages:**
- Slower than BitBlt
- May not work with all applications
- Some applications don't respond to PrintWindow

**Best for:** Capturing windows that might be obscured or minimized

### 3. GdiScreenshotService
**File:** `Services/GdiScreenshotService.cs`

**Description:** Uses .NET's `Graphics.CopyFromScreen` method for simpler screen capture.

**Advantages:**
- Simple .NET-based implementation
- High-quality rendering options
- Good performance
- Easy to understand and maintain

**Disadvantages:**
- Requires window to be visible
- Cannot capture obscured content
- Limited to screen coordinates

**Best for:** Simple screenshot needs where .NET compatibility is preferred

### 4. WinRTScreenshotService
**File:** `Services/WinRTScreenshotService.cs`

**Description:** Hybrid approach using GDI capture with WIC (Windows Imaging Component) processing.

**Advantages:**
- Modern Windows API integration
- Good image quality
- Cross-thread safe (frozen bitmaps)
- WIC-compatible processing

**Disadvantages:**
- More complex implementation
- Requires newer Windows versions
- Similar limitations to GDI methods

**Best for:** Modern applications requiring WIC integration

### 5. DesktopDuplicationScreenshotService
**File:** `Services/DesktopDuplicationScreenshotService.cs`

**Description:** Uses Windows Desktop Duplication API with DirectX for high-performance capture.

**Advantages:**
- Highest performance for frequent captures
- Can capture entire desktop efficiently
- Hardware-accelerated
- Minimal CPU usage

**Disadvantages:**
- Requires DirectX/SharpDX dependencies
- More complex setup and resource management
- Requires Windows 8+ 
- Captures entire screen, then crops to window

**Best for:** High-frequency capture scenarios or when performance is critical

## Dependencies

### Required for all services:
- System.Drawing
- System.Windows.Media.Imaging
- Microsoft.Extensions.Logging

### Additional dependencies:
- **DesktopDuplicationScreenshotService:** SharpDX.Direct3D11, SharpDX.DXGI

## Usage Examples

### Basic Usage
```csharp
// Using dependency injection
var screenshotService = serviceProvider.GetRequiredService<IScreenshotService>();
var screenshot = screenshotService.CaptureWindow(windowHandle);
```

### Testing All Services
```csharp
var tester = new ScreenshotServiceTester(logger);
var results = await tester.TestAllServicesAsync(windowHandle);
tester.PrintResults(results);
```

### Service Registration
```csharp
// In ConfigureServices method
services.AddSingleton<IScreenshotService, BitBltScreenshotService>(); // Default
// or
services.AddSingleton<IScreenshotService, PrintWindowScreenshotService>(); // For obscured windows
```

## Performance Comparison

| Service | Speed | Memory | Compatibility | Special Features |
|---------|-------|--------|---------------|------------------|
| BitBlt | Fast | Low | High | Standard GDI capture |
| PrintWindow | Medium | Low | Medium | Captures obscured windows |
| GDI | Fast | Low | High | .NET native approach |
| WinRT/WIC | Medium | Medium | Medium | Modern API integration |
| Desktop Duplication | Very Fast* | High | Medium | Hardware accelerated |

*For repeated captures; initial setup is slower

## Error Handling

All services include comprehensive error handling:
- Window validation (IsWindow, IsWindowVisible)
- Dimension validation
- Resource cleanup in finally blocks
- Detailed logging for troubleshooting

## Choosing the Right Service

1. **Default choice:** `BitBltScreenshotService` - reliable and fast
2. **Obscured windows:** `PrintWindowScreenshotService` 
3. **Simple .NET:** `GdiScreenshotService`
4. **Modern integration:** `WinRTScreenshotService`
5. **High performance:** `DesktopDuplicationScreenshotService` (with SharpDX)

## Testing

Use the `ScreenshotServiceTester` class to compare all services:

```bash
# Test with current foreground window
dotnet run

# Test with specific window
dotnet run "Notepad"
```

The tester provides detailed metrics including:
- Success/failure status
- Execution time
- Image dimensions
- Pixel format
- Error messages

## Future Enhancements

Potential improvements:
1. Async versions of capture methods
2. Region-based capture (partial window)
3. Multiple monitor support
4. Image format options (PNG, JPEG, etc.)
5. Compression settings
6. Batch capture capabilities
