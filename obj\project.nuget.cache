{"version": 2, "dgSpecHash": "oOBPV0b7CQo=", "success": true, "projectFilePath": "D:\\Projects\\Code\\Cowpilot\\Cowpilot.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.mvvm\\8.4.0\\communitytoolkit.mvvm.8.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.5\\microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.5\\microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.5\\microsoft.extensions.logging.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.5\\microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.5\\microsoft.extensions.options.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.5\\microsoft.extensions.primitives.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.toolkit.uwp.notifications\\7.1.3\\microsoft.toolkit.uwp.notifications.7.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\9.0.5\\microsoft.win32.systemevents.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4\\4.11.0.20250507\\opencvsharp4.4.11.0.20250507.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4.runtime.win\\4.11.0.20250507\\opencvsharp4.runtime.win.4.11.0.20250507.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4.wpfextensions\\4.11.0.20250507\\opencvsharp4.wpfextensions.4.11.0.20250507.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.mkldnn.runtime.win64\\0.19.0\\sdcb.mkldnn.runtime.win64.0.19.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.onnx.runtime.win64\\1.11.22.423\\sdcb.onnx.runtime.win64.1.11.22.423.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.paddle2onnx.runtime.win64\\1.0.0-rc.2\\sdcb.paddle2onnx.runtime.win64.1.0.0-rc.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.paddleinference\\2.5.0.1\\sdcb.paddleinference.2.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.paddleinference.runtime.win64.cu120-sm86-89\\2.6.1\\sdcb.paddleinference.runtime.win64.cu120-sm86-89.2.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.paddleocr\\2.7.0.3\\sdcb.paddleocr.2.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.paddleocr.models.local\\2.7.0\\sdcb.paddleocr.models.local.2.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.paddleocr.models.localv3\\2.7.0\\sdcb.paddleocr.models.localv3.2.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.paddleocr.models.localv4\\2.7.0.1\\sdcb.paddleocr.models.localv4.2.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sdcb.paddleocr.models.shared\\2.7.0.1\\sdcb.paddleocr.models.shared.2.7.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\9.0.5\\system.drawing.common.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.6.3\\system.memory.4.6.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.22621.57\\microsoft.windows.sdk.net.ref.10.0.22621.57.nupkg.sha512"], "logs": []}