﻿using System.Windows;
using Cowpilot.Interfaces;
using Cowpilot.Services;
using Cowpilot.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace Cowpilot
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IServiceProvider? _serviceProvider;

        private void ConfigureServices(IServiceCollection services)
        {
            services.AddLogging();

            services.AddSingleton<IOCRService, PaddleOCRService>();
            services.AddSingleton<IScreenshotService, BitBltScreenshotService>();
            services.AddSingleton<IItemScannerService, OCRItemScannerService>();
            services.AddSingleton<INotificationService, WindowsNotificationService>();

            services.AddTransient<IMainWindowViewModel, MailWindowViewModel>();

            services.AddSingleton<MainWindow>();
        }

        [STAThread]
        protected override void OnStartup(StartupEventArgs e)
        {
            var serviceCollection = new ServiceCollection();
            ConfigureServices(serviceCollection);

            _serviceProvider = serviceCollection.BuildServiceProvider();

            IOCRService ocrService = _serviceProvider.GetRequiredService<IOCRService>();
            ocrService.InitializeAsync().Wait();

            var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
    }
}
