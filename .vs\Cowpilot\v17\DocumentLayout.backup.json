{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\Code\\Cowpilot\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\services\\ocritemscannerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:services\\ocritemscannerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\services\\bitbltscreenshotservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:services\\bitbltscreenshotservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\views\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:views\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\views\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:views\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\viewmodels\\mailwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:viewmodels\\mailwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\interfaces\\imainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:interfaces\\imainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\services\\paddleocrservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:services\\paddleocrservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\services\\windowsnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:services\\windowsnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|d:\\projects\\code\\cowpilot\\app.xaml.cs||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{FF2BF902-2248-45AF-9F27-6020DFBE32A1}|Cowpilot.csproj|solutionrelative:app.xaml.cs||{8B382828-6202-11D1-8870-0000F87579D2}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 246, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "OCRItemScannerService.cs", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\Services\\OCRItemScannerService.cs", "RelativeDocumentMoniker": "Services\\OCRItemScannerService.cs", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\Services\\OCRItemScannerService.cs", "RelativeToolTip": "Services\\OCRItemScannerService.cs", "ViewState": "AgIAAJYAAAAAAAAAAAAYwKwAAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T16:28:20.786Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "PaddleOCRService.cs", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\Services\\PaddleOCRService.cs", "RelativeDocumentMoniker": "Services\\PaddleOCRService.cs", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\Services\\PaddleOCRService.cs", "RelativeToolTip": "Services\\PaddleOCRService.cs", "ViewState": "AgIAABgAAAAAAAAAAAAYwDkAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T16:36:12.487Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "BitBltScreenshotService.cs", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\Services\\BitBltScreenshotService.cs", "RelativeDocumentMoniker": "Services\\BitBltScreenshotService.cs", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\Services\\BitBltScreenshotService.cs", "RelativeToolTip": "Services\\BitBltScreenshotService.cs", "ViewState": "AgIAAHQAAAAAAAAAAAAawHkAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T16:28:06.295Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\Views\\MainWindow.xaml", "RelativeDocumentMoniker": "Views\\MainWindow.xaml", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\Views\\MainWindow.xaml", "RelativeToolTip": "Views\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-01T17:06:47.135Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "MailWindowViewModel.cs", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\ViewModels\\MailWindowViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\MailWindowViewModel.cs", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\ViewModels\\MailWindowViewModel.cs", "RelativeToolTip": "ViewModels\\MailWindowViewModel.cs", "ViewState": "AgIAABkAAAAAAAAAAAAQwBcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T16:54:04.312Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "IMainWindowViewModel.cs", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\Interfaces\\IMainWindowViewModel.cs", "RelativeDocumentMoniker": "Interfaces\\IMainWindowViewModel.cs", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\Interfaces\\IMainWindowViewModel.cs", "RelativeToolTip": "Interfaces\\IMainWindowViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T16:53:51.345Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\Views\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "Views\\MainWindow.xaml.cs", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\Views\\MainWindow.xaml.cs", "RelativeToolTip": "Views\\MainWindow.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T16:44:51.995Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\App.xaml.cs", "RelativeDocumentMoniker": "App.xaml.cs", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\App.xaml.cs", "RelativeToolTip": "App.xaml.cs", "ViewState": "AgIAAAYAAAAAAAAAAIBGwDUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T16:27:45.694Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "WindowsNotificationService.cs", "DocumentMoniker": "D:\\Projects\\Code\\Cowpilot\\Services\\WindowsNotificationService.cs", "RelativeDocumentMoniker": "Services\\WindowsNotificationService.cs", "ToolTip": "D:\\Projects\\Code\\Cowpilot\\Services\\WindowsNotificationService.cs", "RelativeToolTip": "Services\\WindowsNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T16:25:09.483Z"}]}]}]}