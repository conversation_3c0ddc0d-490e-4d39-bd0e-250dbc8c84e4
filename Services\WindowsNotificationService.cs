﻿using Cowpilot.Interfaces;
using Microsoft.Toolkit.Uwp.Notifications;

namespace Cowpilot.Services
{
    public class WindowsNotificationService : INotificationService
    {
        public void Notify(string notificationTitle, string? notificationText = null)
        {
            var builder = new ToastContentBuilder().AddText(notificationTitle);
            if (!string.IsNullOrEmpty(notificationText))
            {
                builder.AddText(notificationText);
            }

            builder.Show();
        }
    }
}
