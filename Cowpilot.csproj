﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows$([Microsoft.Build.Utilities.ToolLocationHelper]::GetLatestSDKTargetPlatformVersion('Windows', '10.0'))</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageReference Include="Microsoft.Toolkit.Uwp.Notifications" Version="7.1.3" />
    <PackageReference Include="OpenCvSharp4" Version="4.11.0.20250507" />
    <PackageReference Include="OpenCvSharp4.runtime.win" Version="4.11.0.20250507" />
    <PackageReference Include="OpenCvSharp4.WpfExtensions" Version="4.11.0.20250507" />
    <PackageReference Include="Sdcb.PaddleInference.runtime.win64.cu120-sm86-89" Version="2.6.1" />
    <PackageReference Include="Sdcb.PaddleOCR" Version="2.7.0.3" />
    <PackageReference Include="Sdcb.PaddleOCR.Models.Local" Version="2.7.0" />
    <PackageReference Include="Sdcb.PaddleOCR.Models.LocalV4" Version="2.7.0.1" />
    <PackageReference Include="Sdcb.PaddleOCR.Models.Shared" Version="2.7.0.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.5" />
  </ItemGroup>

</Project>
