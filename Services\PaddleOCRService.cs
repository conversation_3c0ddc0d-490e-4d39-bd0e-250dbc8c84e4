﻿using System.Windows.Media.Imaging;
using Cowpilot.Interfaces;
using Cowpilot.Models;
using Microsoft.Extensions.Logging;
using OpenCvSharp;
using OpenCvSharp.WpfExtensions;
using Sdcb.PaddleInference;
using Sdcb.PaddleOCR;
using Sdcb.PaddleOCR.Models.Local;

namespace Cowpilot.Services
{
    public class PaddleOCRService : IOCRService
    {
        private readonly ILogger<PaddleOCRService> _logger;
        private PaddleOcrAll? _paddleEngine;

        public PaddleOCRService(ILogger<PaddleOCRService> logger)
        {
            _logger = logger;
        }

        public Task InitializeAsync()
        {
            if (_paddleEngine != null)
            {
                return Task.CompletedTask;
            }

            _paddleEngine = new PaddleOcrAll(LocalFullModels.EnglishV4, PaddleDevice.Gpu())
            {
                AllowRotateDetection = false,
                Enable180Classification = false,
            };

            return Task.CompletedTask;
        }

        public Task<IEnumerable<OCRResult>> PerformOCRAsync(BitmapSource bitmapSource)
        {
            if (_paddleEngine == null)
            {
                _logger.LogError("Paddle OCR is not initialized.");
                throw new ApplicationException("Paddle OCR is not initialized.");
            }

            using Mat mat = bitmapSource.ToMat();
            using Mat gray = new();

            Cv2.CvtColor(mat, gray, ColorConversionCodes.BGR2GRAY);

            PaddleOcrResult paddleResult = _paddleEngine.Run(gray);
            IEnumerable<OCRResult> ocrResult = paddleResult
                .Regions.OrderBy(r => r.Rect.Center.Y)
                .ThenBy(r => r.Rect.Center.X)
                .Select(r => OCRResult.FromPaddleOcrResultRegion(r));

            return Task.FromResult(ocrResult);
        }
    }
}
