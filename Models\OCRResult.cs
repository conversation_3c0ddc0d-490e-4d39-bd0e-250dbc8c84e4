﻿using System.Drawing;
using Sdcb.PaddleOCR;

namespace Cowpilot.Models
{
    public record OCRResult(string Text, Rectangle? Region)
    {
        public static OCRResult FromPaddleOcrResultRegion(PaddleOcrResultRegion ocrResultRegion)
        {
            var text = string.IsNullOrWhiteSpace(ocrResultRegion.Text)
                ? string.Empty
                : ocrResultRegion.Text.Trim();
            var region = new Rectangle(
                (int)ocrResultRegion.Rect.Center.X - (int)(ocrResultRegion.Rect.Size.Width * 0.5),
                (int)ocrResultRegion.Rect.Center.Y - (int)(ocrResultRegion.Rect.Size.Height * 0.5),
                (int)ocrResultRegion.Rect.Size.Width,
                (int)ocrResultRegion.Rect.Size.Height
            );

            return new OCRResult(text, region);
        }
    }
}
