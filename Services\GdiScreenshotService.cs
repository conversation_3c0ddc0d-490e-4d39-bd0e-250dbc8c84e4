using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using Cowpilot.Interfaces;
using Microsoft.Extensions.Logging;

namespace Cowpilot.Services
{
    public class GdiScreenshotService : IScreenshotService
    {
        private readonly ILogger<GdiScreenshotService> _logger;

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern bool IsWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
            public readonly int Width => Right - Left;
            public readonly int Height => Bottom - Top;
        }

        public GdiScreenshotService(ILogger<GdiScreenshotService> logger)
        {
            _logger = logger;
        }

        public BitmapSource? CaptureWindow(IntPtr hWnd)
        {
            try
            {
                // Validate window handle
                if (!IsWindow(hWnd))
                {
                    _logger.LogError("Invalid window handle provided.");
                    return null;
                }

                if (!IsWindowVisible(hWnd))
                {
                    _logger.LogWarning("Window is not visible. CopyFromScreen requires visible windows.");
                    return null;
                }

                // Get window dimensions and position
                if (!GetWindowRect(hWnd, out RECT rect))
                {
                    _logger.LogError("Failed to get window rectangle.");
                    return null;
                }

                if (rect.Width <= 0 || rect.Height <= 0)
                {
                    _logger.LogError("Window has invalid dimensions: {Width}x{Height}", rect.Width, rect.Height);
                    return null;
                }

                // Create bitmap and use Graphics.CopyFromScreen
                using var bitmap = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);
                using var graphics = Graphics.FromImage(bitmap);
                
                // Set high quality settings for better capture
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

                // Copy from screen at the window's position
                graphics.CopyFromScreen(
                    rect.Left, 
                    rect.Top, 
                    0, 
                    0, 
                    new System.Drawing.Size(rect.Width, rect.Height),
                    CopyPixelOperation.SourceCopy
                );

                // Convert to BitmapSource
                IntPtr hBitmap = bitmap.GetHbitmap();
                try
                {
                    BitmapSource bitmapSource = Imaging.CreateBitmapSourceFromHBitmap(
                        hBitmap,
                        IntPtr.Zero,
                        Int32Rect.Empty,
                        BitmapSizeOptions.FromEmptyOptions()
                    );

                    // Freeze the bitmap to make it cross-thread accessible
                    bitmapSource.Freeze();
                    return bitmapSource;
                }
                finally
                {
                    // Clean up the HBITMAP
                    DeleteObject(hBitmap);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while capturing window screenshot using GDI.");
                return null;
            }
        }
    }
}
