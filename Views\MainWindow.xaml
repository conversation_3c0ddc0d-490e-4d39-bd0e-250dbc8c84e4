﻿<Window x:Class="Cowpilot.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Cowpilot"
        mc:Ignorable="d"
        Title="MainWindow"
        SizeToContent="WidthAndHeight"
        Topmost="True">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition/>
            <RowDefinition/>
        </Grid.RowDefinitions>
        
        <Label Grid.Row="0" Margin="8, 8, 8, 4">Found Items:</Label>
        <Border Grid.Row="1" Margin="8, 0, 8, 8" BorderThickness="1" BorderBrush="DimGray" Padding="4" CornerRadius="4">
            <ItemsControl ItemsSource="{Binding FoundItems}" Width="256">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
            </ItemsControl>
        </Border>
        <Button Grid.Row="2" Width="128" Height="32" Margin="8, 8, 8, 8" Content="{Binding BtnScanText}" Command="{Binding ClickScanCommand}"></Button>
    </Grid>
</Window>
