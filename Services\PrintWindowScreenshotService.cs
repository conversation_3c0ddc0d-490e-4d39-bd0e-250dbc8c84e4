using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using Cowpilot.Interfaces;
using Microsoft.Extensions.Logging;

namespace Cowpilot.Services
{
    public class PrintWindowScreenshotService : IScreenshotService
    {
        private readonly ILogger<PrintWindowScreenshotService> _logger;

        [DllImport("user32.dll")]
        private static extern bool PrintWindow(IntPtr hWnd, IntPtr hdcBlt, uint nFlags);

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern bool IsWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
            public readonly int Width => Right - Left;
            public readonly int Height => Bottom - Top;
        }

        // PrintWindow flags
        private const uint PW_CLIENTONLY = 0x1;
        private const uint PW_RENDERFULLCONTENT = 0x2;

        public PrintWindowScreenshotService(ILogger<PrintWindowScreenshotService> logger)
        {
            _logger = logger;
        }

        public BitmapSource? CaptureWindow(IntPtr hWnd)
        {
            try
            {
                // Validate window handle
                if (!IsWindow(hWnd))
                {
                    _logger.LogError("Invalid window handle provided.");
                    return null;
                }

                if (!IsWindowVisible(hWnd))
                {
                    _logger.LogWarning("Window is not visible, but attempting capture anyway.");
                }

                // Get window dimensions
                if (!GetWindowRect(hWnd, out RECT rect))
                {
                    _logger.LogError("Failed to get window rectangle.");
                    return null;
                }

                if (rect.Width <= 0 || rect.Height <= 0)
                {
                    _logger.LogError("Window has invalid dimensions: {Width}x{Height}", rect.Width, rect.Height);
                    return null;
                }

                // Create bitmap to capture into
                using var bitmap = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);
                using var graphics = Graphics.FromImage(bitmap);
                
                IntPtr hdc = graphics.GetHdc();
                try
                {
                    // Use PrintWindow to capture the window content
                    // PW_RENDERFULLCONTENT ensures we get the full content even if window is partially obscured
                    bool success = PrintWindow(hWnd, hdc, PW_RENDERFULLCONTENT);
                    
                    if (!success)
                    {
                        _logger.LogError("PrintWindow failed to capture window content.");
                        return null;
                    }
                }
                finally
                {
                    graphics.ReleaseHdc(hdc);
                }

                // Convert to BitmapSource
                IntPtr hBitmap = bitmap.GetHbitmap();
                try
                {
                    BitmapSource bitmapSource = Imaging.CreateBitmapSourceFromHBitmap(
                        hBitmap,
                        IntPtr.Zero,
                        Int32Rect.Empty,
                        BitmapSizeOptions.FromEmptyOptions()
                    );

                    // Freeze the bitmap to make it cross-thread accessible
                    bitmapSource.Freeze();
                    return bitmapSource;
                }
                finally
                {
                    // Clean up the HBITMAP
                    DeleteObject(hBitmap);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while capturing window screenshot.");
                return null;
            }
        }
    }
}
