﻿using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using Cowpilot.Interfaces;
using Microsoft.Extensions.Logging;

namespace Cowpilot.Services
{
    public class BitBltScreenshotService : IScreenshotService
    {
        private readonly ILogger<BitBltScreenshotService> _logger;

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowDC(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern int ReleaseDC(IntPtr hWnd, IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleDC(IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleBitmap(IntPtr hDC, int nWidth, int nHeight);

        [DllImport("gdi32.dll")]
        private static extern IntPtr SelectObject(IntPtr hDC, IntPtr hObject);

        [DllImport("gdi32.dll")]
        private static extern bool BitBlt(
            IntPtr hdcDest,
            int nXDest,
            int nYDest,
            int nWidth,
            int nHeight,
            IntPtr hdcSrc,
            int nXSrc,
            int nYSrc,
            CopyPixelOperation dwRop
        );

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteDC(IntPtr hdc);

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
            public readonly int Width => Right - Left;
            public readonly int Height => Bottom - Top;
        }

        public BitBltScreenshotService(ILogger<BitBltScreenshotService> logger)
        {
            _logger = logger;
        }

        public BitmapSource? CaptureWindow(IntPtr hWnd)
        {
            var hWndDC = IntPtr.Zero;
            var hMemDC = IntPtr.Zero;
            var hBitmap = IntPtr.Zero;
            var hOld = IntPtr.Zero;

            try
            {
                hWndDC = GetWindowDC(hWnd);
                if (hWndDC == IntPtr.Zero)
                {
                    _logger.LogError("Failed to get window DC.");
                    return null;
                }

                if (!GetWindowRect(hWnd, out RECT rect))
                {
                    _logger.LogError("Failed to get client rect.");
                    return null;
                }

                hMemDC = CreateCompatibleDC(hWndDC);
                if (hMemDC == IntPtr.Zero)
                {
                    _logger.LogError("Failed to create compatible DC.");
                    return null;
                }

                hBitmap = CreateCompatibleBitmap(hWndDC, rect.Width, rect.Height);
                if (hBitmap == IntPtr.Zero)
                {
                    _logger.LogError("Failed to create compatible bitmap.");
                    return null;
                }

                hOld = SelectObject(hMemDC, hBitmap);
                if (hOld == IntPtr.Zero)
                {
                    _logger.LogError("Failed to select bitmap.");
                    return null;
                }

                bool success = BitBlt(
                    hMemDC,
                    0,
                    0,
                    rect.Width,
                    rect.Height,
                    hWndDC,
                    0,
                    0,
                    CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt
                );
                if (!success)
                {
                    _logger.LogError("BitBlt failed.");
                    return null;
                }

                BitmapSource screenshot = Imaging.CreateBitmapSourceFromHBitmap(
                    hBitmap,
                    IntPtr.Zero,
                    Int32Rect.Empty,
                    BitmapSizeOptions.FromEmptyOptions()
                );

                return screenshot;
            }
            catch
            {
                return null;
            }
            finally
            {
                if (hOld != IntPtr.Zero)
                {
                    SelectObject(hMemDC, hOld);
                }

                if (hBitmap != IntPtr.Zero)
                {
                    DeleteObject(hBitmap);
                }

                if (hMemDC != IntPtr.Zero)
                {
                    DeleteDC(hMemDC);
                }

                if (hWndDC != IntPtr.Zero)
                {
                    ReleaseDC(hWnd, hWndDC);
                }
            }
        }
    }
}
