using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using Cowpilot.Interfaces;
using Microsoft.Extensions.Logging;

// NOTE: This service requires SharpDX packages to be installed:
// - SharpDX.Direct3D11
// - SharpDX.DXGI
//
// To enable this service, add the following packages to your project:
// <PackageReference Include="SharpDX.Direct3D11" Version="4.2.0" />
// <PackageReference Include="SharpDX.DXGI" Version="4.2.0" />
//
// Then uncomment the using statements below:
// using SharpDX;
// using SharpDX.Direct3D11;
// using SharpDX.DXGI;

namespace Cowpilot.Services
{
    /// <summary>
    /// Desktop Duplication API screenshot service - REQUIRES SharpDX packages
    /// This service is currently disabled due to missing dependencies.
    /// Install SharpDX.Direct3D11 and SharpDX.DXGI packages to enable.
    /// </summary>
    public class DesktopDuplicationScreenshotService : IScreenshotService, IDisposable
    {
        private readonly ILogger<DesktopDuplicationScreenshotService> _logger;
        // private SharpDX.Direct3D11.Device? _device;
        // private OutputDuplication? _outputDuplication;
        private bool _disposed = false;

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern bool IsWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr MonitorFromWindow(IntPtr hwnd, uint dwFlags);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
            public readonly int Width => Right - Left;
            public readonly int Height => Bottom - Top;
        }

        private const uint MONITOR_DEFAULTTONEAREST = 2;

        public DesktopDuplicationScreenshotService(ILogger<DesktopDuplicationScreenshotService> logger)
        {
            _logger = logger;
            _logger.LogWarning("DesktopDuplicationScreenshotService is disabled due to missing SharpDX dependencies.");
        }

        public BitmapSource? CaptureWindow(IntPtr hWnd)
        {
            _logger.LogError("DesktopDuplicationScreenshotService is not available. Install SharpDX packages to enable this service.");
            return null;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                // Nothing to dispose when SharpDX is not available
                _disposed = true;
            }
        }
    }
}
