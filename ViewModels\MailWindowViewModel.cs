﻿using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Cowpilot.Interfaces;
using Cowpilot.Services;

namespace Cowpilot.ViewModels
{
    public class MailWindowViewModel : ObservableRecipient, IMainWindowViewModel
    {
        private readonly IItemScannerService _itemScannerService;
        private bool _isScanning;

        public MailWindowViewModel(IItemScannerService itemScannerService)
        {
            _itemScannerService = itemScannerService;

            ClickScanCommand = new AsyncRelayCommand(ClickScanAsync);

            BtnScanText = "Start scanning";
        }

        private string _btnScanText = string.Empty;

        public string BtnScanText
        {
            get => _btnScanText;
            set => SetProperty(ref _btnScanText, value);
        }

        public IAsyncRelayCommand ClickScanCommand { get; }

        public ObservableCollection<string> FoundItems { get; } = [];

        private async Task ClickScanAsync()
        {
            if (_isScanning)
            {
                _itemScannerService.FoundItem -= HandleFoundItem;
                _itemScannerService.LostItem -= HandleLostItem;
                await _itemScannerService.StopScanAsync();
                FoundItems.Clear();
                BtnScanText = "Start scanning";
                _isScanning = false;
            }
            else
            {
                _itemScannerService.FoundItem += HandleFoundItem;
                _itemScannerService.LostItem += HandleLostItem;
                _itemScannerService.StartScan();
                BtnScanText = "Stop scanning";
                _isScanning = true;
            }
        }

        private void HandleFoundItem(object? sender, EventArgs e)
        {
            var args = (FoundItemArgs)e;
            FoundItems.Add(args.Item);
        }

        private void HandleLostItem(object? sender, EventArgs e)
        {
            var args = (LostItemArgs)e;
            FoundItems.Remove(args.Item);
        }
    }
}
