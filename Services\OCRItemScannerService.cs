﻿using System.Collections.Immutable;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Media.Imaging;
using Cowpilot.Interfaces;
using Cowpilot.Models;
using Microsoft.Extensions.Logging;

namespace Cowpilot.Services
{
    public class FoundItemArgs : EventArgs
    {
        public FoundItemArgs(string item) => Item = item;

        public string Item { get; private set; }
    }

    public class LostItemArgs : EventArgs
    {
        public LostItemArgs(string item) => Item = item;

        public string Item { get; private set; }
    }

    public class OCRItemScannerService : IItemScannerService
    {
        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string? lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern bool IsIconic(IntPtr hWnd);

        private readonly IScreenshotService _screenshotService;
        private readonly IOCRService _ocrService;
        private readonly INotificationService _notificationService;
        private readonly ILogger<OCRItemScannerService> _logger;
        private readonly PeriodicTimer _scanTimer;
        private readonly CancellationTokenSource _cts = new();
        private readonly string _windowTitle = "MU";
        private readonly IImmutableList<string> _targetItems =
        [
            "Jewel of Chaos",
            "Jewel of Bless",
            "Jewel of Soul",
            "Jewel of Life",
            "Jewel of Creation",
            "Gemstone",
            "Jewel of Harmony",
            "Jewel of Guardian",
            "Jewel of Goldsmith",
            "Jewel of Ancient Harmony",
            "Jewel of Luck",
            "Golden Medal",
            "Star of Sacred Birth",
            "Red Ribbon Box",
            "Lilac Candy Box",
            "Red Chocolate Box",
            "Blue Chocolate Box",
            "Pink Chocolate Box",
            "Box of Luck",
            "Violet Mistery Box",
            "Navy Candy Box",
            "Orange Candy Box",
            "Box of Kundun +1",
            "Box of Kundun +2",
            "Box of Kundun +3",
            "Box of Kundun +4",
            "Box of Kundun +5",
            "Pink Mistery Box",
            "Green Ribbon Box",
            "Blue Ribbon Box",
            "Green Mistery Box",
            "Symbol of Kundun +1",
            "Symbol of Kundun +2",
            "Symbol of Kundun +3",
            "Symbol of Kundun +4",
            "Symbol of Kundun +5",
            "Symbol of Kundun +6",
            "Symbol of Kundun +7",
            "Devil Eye +5",
            "Devil Eye +6",
            "Devil Key +5",
            "Devil Key +6",
            "Scroll of Archangel +6",
            "Scroll of Archangel +7",
            "Blood Bone +6",
            "Blood Bone +7",
            "Shard of Condor",
            "Dark spirit Spirit",
            "Dark horse Spirit",
            "Splinter of Armor",
            "Bless of Guardian",
            "Claw of Beast",
            "Ring of Magic",
            "Illusion of Kundun",
            "Zaikan",
            "Ice Warrior",
        ];
        private Task? _scanTask;
        private IDictionary<string, int> _currentItemCounts;

        // TODO: Load from settings
        public OCRItemScannerService(
            IScreenshotService screenshotService,
            IOCRService ocrService,
            INotificationService notificationService,
            ILogger<OCRItemScannerService> logger
        )
        {
            _screenshotService = screenshotService;
            _ocrService = ocrService;
            _notificationService = notificationService;
            _logger = logger;
            _scanTimer = new PeriodicTimer(TimeSpan.FromSeconds(3));
        }

        public event EventHandler FoundItem;
        public event EventHandler LostItem;

        public void StartScan()
        {
            _scanTask = DoScanAsync();
        }

        public async Task StopScanAsync()
        {
            if (_scanTask is null)
            {
                return;
            }

            _cts.Cancel();
            await _scanTask;
            _cts.Dispose();
            _currentItemCounts.Clear();
        }

        private async Task DoScanAsync()
        {
            try
            {
                while (await _scanTimer.WaitForNextTickAsync(_cts.Token))
                {
                    IntPtr gameWindowHandle = FindWindow(null, _windowTitle);
                    if (gameWindowHandle == IntPtr.Zero)
                    {
                        _notificationService.Notify("Could not find game window.");
                        _logger.LogWarning("Could not find game window.");
                        continue;
                    }

                    if (IsIconic(gameWindowHandle))
                    {
                        _notificationService.Notify(
                            "Game window is minimized",
                            "Scanning for items while minimized is not possible"
                        );
                        _logger.LogWarning("Game window is minimized.");
                        continue;
                    }

                    BitmapSource? gameScreenshot = _screenshotService.CaptureWindow(
                        gameWindowHandle
                    );
                    if (gameScreenshot == null)
                    {
                        _notificationService.Notify("Failed to capture game screenshot");
                        _logger.LogError("Failed to capute game screenshot.");
                        continue;
                    }

                    // Starts with is fine, but we should only allow longer text if it starts with a +
                    var ocrResult = await _ocrService.PerformOCRAsync(gameScreenshot);
                    using (var fileStream = new FileStream("test.png", FileMode.Create))
                    {
                        BitmapEncoder encoder = new PngBitmapEncoder();
                        encoder.Frames.Add(BitmapFrame.Create(gameScreenshot));
                        encoder.Save(fileStream);
                    }
                    var newItemCounts = BuildItemCountDictionary(
                        ocrResult.Where(r => _targetItems.Any(t => IsMatch(r.Text.Trim(), t, 1)))
                    );

                    HandleNewItemCountDictionary(newItemCounts);
                }
            }
            catch (OperationCanceledException) { }
        }

        /// <summary>
        /// Checks if a given string should match based on given control string, allowing for a specified number of errors
        /// in alphabetical characters only.
        /// </summary>
        /// <param name="input">The input string to check</param>
        /// <param name="control">The control string to match against</param>
        /// <param name="maxErrors">Maximum number of alphabetical character errors allowed</param>
        /// <returns>True if the input starts with the control string within the error tolerance</returns>
        public static bool IsMatch(string input, string control, int maxErrors = 0)
        {
            if (!StartsWithFuzzy(input, control, maxErrors))
            {
                return false;
            }

            // Match only if anything following control string starts with a '+'
            return input[control.Length..].Trim().FirstOrDefault() == '+';
        }

        /// <summary>
        /// Checks if a string starts with another string, allowing for a specified number of errors
        /// in alphabetical characters only.
        /// </summary>
        /// <param name="input">The input string to check</param>
        /// <param name="control">The control string to match against</param>
        /// <param name="maxErrors">Maximum number of alphabetical character errors allowed</param>
        /// <returns>True if the input starts with the control string within the error tolerance</returns>
        public static bool StartsWithFuzzy(string input, string control, int maxErrors = 0)
        {
            // Handle null or empty cases
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(control))
                return string.IsNullOrEmpty(control);

            // Input must be at least as long as control string
            if (input.Length < control.Length)
                return false;

            int errorCount = 0;

            // Compare characters up to the length of the control string
            for (int i = 0; i < control.Length; i++)
            {
                char inputChar = input[i];
                char controlChar = control[i];

                // If characters don't match
                if (inputChar != controlChar)
                {
                    // Check if both characters are alphabetical
                    if (char.IsLetter(inputChar) && char.IsLetter(controlChar))
                    {
                        errorCount++;

                        // Exceed maximum allowed errors
                        if (errorCount > maxErrors)
                            return false;
                    }
                    else
                    {
                        // Non-alphabetical characters must match exactly
                        return false;
                    }
                }
            }

            return true;
        }

        private void HandleNewItemCountDictionary(IDictionary<string, int> newItemCounts)
        {
            if (_currentItemCounts is null)
            {
                _currentItemCounts = newItemCounts;
                return;
            }

            if (_currentItemCounts.Count == 0 && newItemCounts.Count == 0)
            {
                return;
            }

            Dictionary<string, int> itemCountDifferences = new();
            var allKeys = new HashSet<string>(_currentItemCounts.Keys);
            allKeys.UnionWith(newItemCounts.Keys);
            foreach (var item in allKeys)
            {
                int currentCount = _currentItemCounts.ContainsKey(item)
                    ? _currentItemCounts[item]
                    : 0;
                int newCount = newItemCounts.ContainsKey(item) ? newItemCounts[item] : 0;
                itemCountDifferences[item] = newCount - currentCount;
            }

            foreach (var (item, count) in itemCountDifferences)
            {
                if (count > 0)
                {
                    _notificationService.Notify($"Found a new item: {item}");
                    OnFoundItem(item);
                }
                else if (count < 0)
                {
                    OnLostItem(item);
                }
            }

            _currentItemCounts = newItemCounts;
        }

        private IDictionary<string, int> BuildItemCountDictionary(IEnumerable<OCRResult> ocrResult)
        {
            Dictionary<string, int> itemCounts = new();
            foreach (OCRResult result in ocrResult)
            {
                if (itemCounts.TryGetValue(result.Text, out var _))
                {
                    itemCounts[result.Text]++;
                }
                else
                {
                    itemCounts[result.Text] = 1;
                }
            }

            return itemCounts;
        }

        protected virtual void OnFoundItem(string item)
        {
            FoundItem?.Invoke(this, new FoundItemArgs(item));
        }

        protected virtual void OnLostItem(string item)
        {
            LostItem?.Invoke(this, new LostItemArgs(item));
        }
    }
}
