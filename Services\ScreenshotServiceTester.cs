using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Media.Imaging;
using Cowpilot.Interfaces;
using Microsoft.Extensions.Logging;

namespace Cowpilot.Services
{
    /// <summary>
    /// Test utility class for validating all screenshot services
    /// </summary>
    public class ScreenshotServiceTester
    {
        private readonly ILogger<ScreenshotServiceTester> _logger;
        private readonly List<(string Name, IScreenshotService Service)> _services;

        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string? lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder text, int count);

        [DllImport("user32.dll")]
        private static extern bool IsWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        public ScreenshotServiceTester(ILogger<ScreenshotServiceTester> logger)
        {
            _logger = logger;
            _services = new List<(string, IScreenshotService)>();
            InitializeServices();
        }

        private void InitializeServices()
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());

            // Initialize all available screenshot services
            _services.Add(("BitBlt", new BitBltScreenshotService(loggerFactory.CreateLogger<BitBltScreenshotService>())));
            _services.Add(("PrintWindow", new PrintWindowScreenshotService(loggerFactory.CreateLogger<PrintWindowScreenshotService>())));
            _services.Add(("GDI", new GdiScreenshotService(loggerFactory.CreateLogger<GdiScreenshotService>())));
            _services.Add(("WinRT/WIC", new WinRTScreenshotService(loggerFactory.CreateLogger<WinRTScreenshotService>())));

            // Desktop Duplication service is disabled due to missing SharpDX dependencies
            // To enable: Install SharpDX.Direct3D11 and SharpDX.DXGI packages, then uncomment:
            // _services.Add(("DesktopDuplication", new DesktopDuplicationScreenshotService(loggerFactory.CreateLogger<DesktopDuplicationScreenshotService>())));
        }

        /// <summary>
        /// Test all screenshot services with the current foreground window
        /// </summary>
        public async Task<Dictionary<string, TestResult>> TestAllServicesAsync()
        {
            var results = new Dictionary<string, TestResult>();
            var foregroundWindow = GetForegroundWindow();

            if (foregroundWindow == IntPtr.Zero)
            {
                _logger.LogError("No foreground window found.");
                return results;
            }

            var windowTitle = GetWindowTitle(foregroundWindow);
            _logger.LogInformation("Testing screenshot services with window: {WindowTitle} (Handle: {Handle})", 
                windowTitle, foregroundWindow);

            foreach (var (name, service) in _services)
            {
                results[name] = await TestServiceAsync(name, service, foregroundWindow);
            }

            return results;
        }

        /// <summary>
        /// Test all screenshot services with a specific window handle
        /// </summary>
        public async Task<Dictionary<string, TestResult>> TestAllServicesAsync(IntPtr hWnd)
        {
            var results = new Dictionary<string, TestResult>();

            if (!IsWindow(hWnd))
            {
                _logger.LogError("Invalid window handle provided: {Handle}", hWnd);
                return results;
            }

            var windowTitle = GetWindowTitle(hWnd);
            _logger.LogInformation("Testing screenshot services with window: {WindowTitle} (Handle: {Handle})", 
                windowTitle, hWnd);

            foreach (var (name, service) in _services)
            {
                results[name] = await TestServiceAsync(name, service, hWnd);
            }

            return results;
        }

        /// <summary>
        /// Test a specific screenshot service
        /// </summary>
        private async Task<TestResult> TestServiceAsync(string serviceName, IScreenshotService service, IntPtr hWnd)
        {
            var result = new TestResult { ServiceName = serviceName };
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("Testing {ServiceName} screenshot service...", serviceName);

                var screenshot = await Task.Run(() => service.CaptureWindow(hWnd));
                stopwatch.Stop();

                result.ExecutionTime = stopwatch.Elapsed;

                if (screenshot != null)
                {
                    result.Success = true;
                    result.ImageWidth = screenshot.PixelWidth;
                    result.ImageHeight = screenshot.PixelHeight;
                    result.PixelFormat = screenshot.Format.ToString();
                    
                    _logger.LogInformation("{ServiceName}: SUCCESS - {Width}x{Height} in {Time}ms", 
                        serviceName, result.ImageWidth, result.ImageHeight, result.ExecutionTime.TotalMilliseconds);
                }
                else
                {
                    result.Success = false;
                    result.ErrorMessage = "Service returned null bitmap";
                    _logger.LogWarning("{ServiceName}: FAILED - Service returned null", serviceName);
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.Success = false;
                result.ExecutionTime = stopwatch.Elapsed;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "{ServiceName}: FAILED - Exception occurred", serviceName);
            }

            return result;
        }

        /// <summary>
        /// Find a window by title and test all services
        /// </summary>
        public async Task<Dictionary<string, TestResult>> TestWithWindowTitleAsync(string windowTitle)
        {
            var hWnd = FindWindow(null, windowTitle);
            if (hWnd == IntPtr.Zero)
            {
                _logger.LogError("Window with title '{WindowTitle}' not found.", windowTitle);
                return new Dictionary<string, TestResult>();
            }

            return await TestAllServicesAsync(hWnd);
        }

        /// <summary>
        /// Get window title from handle
        /// </summary>
        private string GetWindowTitle(IntPtr hWnd)
        {
            var title = new System.Text.StringBuilder(256);
            GetWindowText(hWnd, title, title.Capacity);
            return title.ToString();
        }

        /// <summary>
        /// Print test results to console
        /// </summary>
        public void PrintResults(Dictionary<string, TestResult> results)
        {
            Console.WriteLine("\n=== Screenshot Service Test Results ===");
            Console.WriteLine($"{"Service",-20} {"Status",-10} {"Time (ms)",-12} {"Resolution",-15} {"Format",-20} {"Error",-30}");
            Console.WriteLine(new string('-', 120));

            foreach (var result in results.Values.OrderBy(r => r.ServiceName))
            {
                var status = result.Success ? "SUCCESS" : "FAILED";
                var time = result.ExecutionTime.TotalMilliseconds.ToString("F2");
                var resolution = result.Success ? $"{result.ImageWidth}x{result.ImageHeight}" : "N/A";
                var format = result.PixelFormat ?? "N/A";
                var error = result.ErrorMessage ?? "";

                Console.WriteLine($"{result.ServiceName,-20} {status,-10} {time,-12} {resolution,-15} {format,-20} {error,-30}");
            }

            var successCount = results.Values.Count(r => r.Success);
            var totalCount = results.Count;
            Console.WriteLine($"\nSummary: {successCount}/{totalCount} services succeeded");
        }
    }

    /// <summary>
    /// Test result for a screenshot service
    /// </summary>
    public class TestResult
    {
        public string ServiceName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public int ImageWidth { get; set; }
        public int ImageHeight { get; set; }
        public string? PixelFormat { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
