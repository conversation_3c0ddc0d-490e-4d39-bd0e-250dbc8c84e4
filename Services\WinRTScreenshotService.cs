using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using Cowpilot.Interfaces;
using Microsoft.Extensions.Logging;

namespace Cowpilot.Services
{
    /// <summary>
    /// Screenshot service using Windows Media Foundation and WIC (Windows Imaging Component)
    /// This provides a simpler alternative to complex WinRT Graphics Capture APIs
    /// </summary>
    public class WinRTScreenshotService : IScreenshotService
    {
        private readonly ILogger<WinRTScreenshotService> _logger;

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern bool IsWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr GetDC(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern int ReleaseDC(IntPtr hWnd, IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleDC(IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleBitmap(IntPtr hDC, int nWidth, int nHeight);

        [DllImport("gdi32.dll")]
        private static extern IntPtr SelectObject(IntPtr hDC, IntPtr hObject);

        [DllImport("gdi32.dll")]
        private static extern bool BitBlt(IntPtr hdcDest, int nXDest, int nYDest, int nWidth, int nHeight, IntPtr hdcSrc, int nXSrc, int nYSrc, CopyPixelOperation dwRop);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteDC(IntPtr hdc);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
            public readonly int Width => Right - Left;
            public readonly int Height => Bottom - Top;
        }

        public WinRTScreenshotService(ILogger<WinRTScreenshotService> logger)
        {
            _logger = logger;
        }

        public BitmapSource? CaptureWindow(IntPtr hWnd)
        {
            try
            {
                // Validate window handle
                if (!IsWindow(hWnd))
                {
                    _logger.LogError("Invalid window handle provided.");
                    return null;
                }

                if (!IsWindowVisible(hWnd))
                {
                    _logger.LogWarning("Window is not visible.");
                    return null;
                }

                // Get window dimensions
                if (!GetWindowRect(hWnd, out RECT rect))
                {
                    _logger.LogError("Failed to get window rectangle.");
                    return null;
                }

                if (rect.Width <= 0 || rect.Height <= 0)
                {
                    _logger.LogError("Window has invalid dimensions: {Width}x{Height}", rect.Width, rect.Height);
                    return null;
                }

                // Use a hybrid approach: GDI for capture with WIC for processing
                return CaptureUsingWIC(hWnd, rect);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while capturing window screenshot using WinRT/WIC.");
                return null;
            }
        }

        private BitmapSource? CaptureUsingWIC(IntPtr hWnd, RECT rect)
        {
            var hWndDC = IntPtr.Zero;
            var hMemDC = IntPtr.Zero;
            var hBitmap = IntPtr.Zero;
            var hOld = IntPtr.Zero;

            try
            {
                // Get window device context
                hWndDC = GetDC(hWnd);
                if (hWndDC == IntPtr.Zero)
                {
                    _logger.LogError("Failed to get window DC.");
                    return null;
                }

                // Create compatible memory DC
                hMemDC = CreateCompatibleDC(hWndDC);
                if (hMemDC == IntPtr.Zero)
                {
                    _logger.LogError("Failed to create compatible DC.");
                    return null;
                }

                // Create compatible bitmap
                hBitmap = CreateCompatibleBitmap(hWndDC, rect.Width, rect.Height);
                if (hBitmap == IntPtr.Zero)
                {
                    _logger.LogError("Failed to create compatible bitmap.");
                    return null;
                }

                // Select bitmap into memory DC
                hOld = SelectObject(hMemDC, hBitmap);
                if (hOld == IntPtr.Zero)
                {
                    _logger.LogError("Failed to select bitmap.");
                    return null;
                }

                // Perform the bit-block transfer
                bool success = BitBlt(
                    hMemDC, 0, 0, rect.Width, rect.Height,
                    hWndDC, 0, 0, CopyPixelOperation.SourceCopy | CopyPixelOperation.CaptureBlt);

                if (!success)
                {
                    _logger.LogError("BitBlt operation failed.");
                    return null;
                }

                // Create BitmapSource from HBITMAP using WIC-compatible method
                BitmapSource screenshot = Imaging.CreateBitmapSourceFromHBitmap(
                    hBitmap,
                    IntPtr.Zero,
                    Int32Rect.Empty,
                    BitmapSizeOptions.FromEmptyOptions()
                );

                // Freeze for cross-thread access
                screenshot.Freeze();
                return screenshot;
            }
            finally
            {
                // Clean up resources
                if (hOld != IntPtr.Zero)
                    SelectObject(hMemDC, hOld);
                if (hBitmap != IntPtr.Zero)
                    DeleteObject(hBitmap);
                if (hMemDC != IntPtr.Zero)
                    DeleteDC(hMemDC);
                if (hWndDC != IntPtr.Zero)
                    ReleaseDC(hWnd, hWndDC);
            }
        }
    }
}
