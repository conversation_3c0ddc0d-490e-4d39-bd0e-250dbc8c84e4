{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Cowpilot/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Toolkit.Uwp.Notifications": "7.1.3", "OpenCvSharp4": "4.11.0.20250507", "OpenCvSharp4.WpfExtensions": "4.11.0.20250507", "OpenCvSharp4.runtime.win": "4.11.0.20250507", "Sdcb.PaddleInference.runtime.win64.cu120-sm86-89": "2.6.1", "Sdcb.PaddleOCR": "*******", "Sdcb.PaddleOCR.Models.Local": "2.7.0", "Sdcb.PaddleOCR.Models.LocalV4": "*******", "Sdcb.PaddleOCR.Models.Shared": "*******", "System.Drawing.Common": "9.0.5", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.22621.57"}, "runtime": {"Cowpilot.dll": {}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.22621.57": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.22621.38", "fileVersion": "10.0.22621.55"}, "WinRT.Runtime.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.48161"}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.Toolkit.Uwp.Notifications/7.1.3": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Drawing.Common": "9.0.5", "System.Reflection.Emit": "4.7.0", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net5.0-windows10.0.17763/Microsoft.Toolkit.Uwp.Notifications.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "OpenCvSharp4/4.11.0.20250507": {"dependencies": {"System.Memory": "4.6.3"}, "runtime": {"lib/net6.0/OpenCvSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenCvSharp4.runtime.win/4.11.0.20250507": {"runtimeTargets": {"runtimes/win-x64/native/OpenCvSharpExtern.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/opencv_videoio_ffmpeg4110_64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2024.12.0.0"}, "runtimes/win-x86/native/OpenCvSharpExtern.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/opencv_videoio_ffmpeg4110.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2024.12.0.0"}}}, "OpenCvSharp4.WpfExtensions/4.11.0.20250507": {"dependencies": {"OpenCvSharp4": "4.11.0.20250507", "System.Drawing.Common": "9.0.5"}, "runtime": {"lib/net6.0/OpenCvSharp.WpfExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Sdcb.Mkldnn.runtime.win64/0.19.0": {"runtimeTargets": {"runtimes/win-x64/native/libiomp5md.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.0.2019.109"}, "runtimes/win-x64/native/mklml.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Sdcb.Onnx.runtime.win64/1.11.22.423": {"runtimeTargets": {"runtimes/win-x64/native/onnxruntime.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.11.22.423"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Sdcb.Paddle2Onnx.runtime.win64/1.0.0-rc.2": {"runtimeTargets": {"runtimes/win-x64/native/paddle2onnx.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Sdcb.PaddleInference/*******": {"runtime": {"lib/net6.0/Sdcb.PaddleInference.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Sdcb.PaddleInference.runtime.win64.cu120-sm86-89/2.6.1": {"dependencies": {"Sdcb.Mkldnn.runtime.win64": "0.19.0", "Sdcb.Onnx.runtime.win64": "1.11.22.423", "Sdcb.Paddle2Onnx.runtime.win64": "1.0.0-rc.2"}, "runtimeTargets": {"runtimes/win-x64/native/common.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/mkldnn.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "3.2.1.0"}, "runtimes/win-x64/native/paddle_inference_c.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Sdcb.PaddleOCR/*******": {"dependencies": {"OpenCvSharp4": "4.11.0.20250507", "Sdcb.PaddleInference": "*******"}, "runtime": {"lib/netstandard2.0/Sdcb.PaddleOCR.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Sdcb.PaddleOCR.Models.Local/2.7.0": {"dependencies": {"Sdcb.PaddleOCR": "*******", "Sdcb.PaddleOCR.Models.LocalV3": "2.7.0", "Sdcb.PaddleOCR.Models.LocalV4": "*******", "Sdcb.PaddleOCR.Models.Shared": "*******"}, "runtime": {"lib/netstandard2.0/Sdcb.PaddleOCR.Models.Local.dll": {"assemblyVersion": "2.7.0.0", "fileVersion": "2.7.0.0"}}}, "Sdcb.PaddleOCR.Models.LocalV3/2.7.0": {"runtime": {"lib/netstandard2.0/Sdcb.PaddleOCR.Models.LocalV3.dll": {"assemblyVersion": "2.7.0.0", "fileVersion": "2.7.0.0"}}}, "Sdcb.PaddleOCR.Models.LocalV4/*******": {"runtime": {"lib/netstandard2.0/Sdcb.PaddleOCR.Models.LocalV4.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Sdcb.PaddleOCR.Models.Shared/*******": {"runtime": {"lib/netstandard2.0/Sdcb.PaddleOCR.Models.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Drawing.Common/9.0.5": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.5"}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21601"}, "lib/net9.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21601"}}}, "System.Memory/4.6.3": {}, "System.Reflection.Emit/4.7.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.ValueTuple/4.5.0": {}}}, "libraries": {"Cowpilot/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.22621.57": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.Toolkit.Uwp.Notifications/7.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-A1dglAzb24gjehmb7DwGd07mfyZ1gacAK7ObE0KwDlRc3mayH2QW7cSOy3TkkyELjLg19OQBuhPOj4SpXET9lg==", "path": "microsoft.toolkit.uwp.notifications/7.1.3", "hashPath": "microsoft.toolkit.uwp.notifications.7.1.3.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-D4OYNpmvAsF9MkaY2W8Jue2XuNHDhygvwzo019hs+lP85KaVnOlXmqsjDKr1dHb1DPxDnOKpe6mAgJN7S6ttwg==", "path": "microsoft.win32.systemevents/9.0.5", "hashPath": "microsoft.win32.systemevents.9.0.5.nupkg.sha512"}, "OpenCvSharp4/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-j/R+G6xGC5IV2wGRU0/GF5qG/FrP+Uxp8dmNnXFlIdiw8Gfo4mtvKcBOBfS/bn4pP/7FNHLFX/xvMtgPJeDjAA==", "path": "opencvsharp4/4.11.0.20250507", "hashPath": "opencvsharp4.4.11.0.20250507.nupkg.sha512"}, "OpenCvSharp4.runtime.win/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-3PxMXyzR+pkL9UOx2PiBjZm+/iyCIu5D6OlA1sq9MH7oWrfvVnCXJlUYhIJv67F7SLUwUuGwXDIQBfdsL/54lg==", "path": "opencvsharp4.runtime.win/4.11.0.20250507", "hashPath": "opencvsharp4.runtime.win.4.11.0.20250507.nupkg.sha512"}, "OpenCvSharp4.WpfExtensions/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-MU5V7bULZcGFPjHnOBw1VLwlVKSuEqCPpCP2JAYj/m7bD8ZWNWB1jFNW2OjnonCF01imcTJyeESO7ZDSxDmq8g==", "path": "opencvsharp4.wpfextensions/4.11.0.20250507", "hashPath": "opencvsharp4.wpfextensions.4.11.0.20250507.nupkg.sha512"}, "Sdcb.Mkldnn.runtime.win64/0.19.0": {"type": "package", "serviceable": true, "sha512": "sha512-Dbjg2jje+ZV/PTBVsMoso2gAD8jMlwUdv/0U7ySSeHEKYzXLy83MD9VUZXIhorQLz22x2cnqLGhIxPNjAeVt3A==", "path": "sdcb.mkldnn.runtime.win64/0.19.0", "hashPath": "sdcb.mkldnn.runtime.win64.0.19.0.nupkg.sha512"}, "Sdcb.Onnx.runtime.win64/1.11.22.423": {"type": "package", "serviceable": true, "sha512": "sha512-wAiAxPES9i6qiRvSV8fgaJvU0+moSkHWuIprUJK1A/1RDAfPZBf1WRh1RkxTdmb4Jnd3vEF7DbbM+vEIqOmNGw==", "path": "sdcb.onnx.runtime.win64/1.11.22.423", "hashPath": "sdcb.onnx.runtime.win64.1.11.22.423.nupkg.sha512"}, "Sdcb.Paddle2Onnx.runtime.win64/1.0.0-rc.2": {"type": "package", "serviceable": true, "sha512": "sha512-N9y/jCj+KJ7sw4ZWy0VivJZ6z1J+HTMhI54IxgzwDrhwKMUzGpDkkag9e2zgwXfB6bRNKd05GrlSxOnSMc8Yhw==", "path": "sdcb.paddle2onnx.runtime.win64/1.0.0-rc.2", "hashPath": "sdcb.paddle2onnx.runtime.win64.1.0.0-rc.2.nupkg.sha512"}, "Sdcb.PaddleInference/*******": {"type": "package", "serviceable": true, "sha512": "sha512-mhTFsVo5U06agc31pA0Wb5kX0H328Dqe9985G6D4aGbU0GXzET4ZUyzFgt8jGD1drFezMY4PdZH63lhMTzpnpQ==", "path": "sdcb.paddleinference/*******", "hashPath": "sdcb.paddleinference.*******.nupkg.sha512"}, "Sdcb.PaddleInference.runtime.win64.cu120-sm86-89/2.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-fSXzn3ZZ0ghR3o9S/t6tw4CRVSF68mJhEENtOrQL/pBQKWG7gbhBwwAkLNdiAFDvx9N1zOHdCDdKFTbDMAYqAw==", "path": "sdcb.paddleinference.runtime.win64.cu120-sm86-89/2.6.1", "hashPath": "sdcb.paddleinference.runtime.win64.cu120-sm86-89.2.6.1.nupkg.sha512"}, "Sdcb.PaddleOCR/*******": {"type": "package", "serviceable": true, "sha512": "sha512-6axepRnF/FSn+sdAsF0nuCjPDu8CfbzJe/6puAHFj00Sin4YUcqUTdDZ1UONHRpMkgJYW1qyzjBnvSmBD4szxQ==", "path": "sdcb.paddleocr/*******", "hashPath": "sdcb.paddleocr.*******.nupkg.sha512"}, "Sdcb.PaddleOCR.Models.Local/2.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/qARARoqGy4YWb/drm/gOFbF3M4bejA9LBGP4TRD6unvefOxokzTW0YI2s4fqhaaAOZNObnYgaE1/q+SmP1NQw==", "path": "sdcb.paddleocr.models.local/2.7.0", "hashPath": "sdcb.paddleocr.models.local.2.7.0.nupkg.sha512"}, "Sdcb.PaddleOCR.Models.LocalV3/2.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eqyTyyfOz+cfUvCc7jk1z1eOjUlgmNPyAZzDUqFRv+gs6g2aTdcd0qlWupaAgSDyHOstU/C95PS5hG9jQGakQA==", "path": "sdcb.paddleocr.models.localv3/2.7.0", "hashPath": "sdcb.paddleocr.models.localv3.2.7.0.nupkg.sha512"}, "Sdcb.PaddleOCR.Models.LocalV4/*******": {"type": "package", "serviceable": true, "sha512": "sha512-9zeQs6OY5P4R1dnDddMgmkd73g0KAJ6KIkg4OTxlHcbTiom3Md6sq6bCzJcfClzHFgajepHhZTTUemosuDv6Iw==", "path": "sdcb.paddleocr.models.localv4/*******", "hashPath": "sdcb.paddleocr.models.localv4.*******.nupkg.sha512"}, "Sdcb.PaddleOCR.Models.Shared/*******": {"type": "package", "serviceable": true, "sha512": "sha512-b1h41loXb9w3sgcXRBMtsMPkYI1bN/r+eKpKkFrsopEdUpUQXNRhr2GZ0SqNq9SbN7YzMacVTE4H1O6hBZqx0A==", "path": "sdcb.paddleocr.models.shared/*******", "hashPath": "sdcb.paddleocr.models.shared.*******.nupkg.sha512"}, "System.Drawing.Common/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-T/6nqx0B7/uTe5JjBwrKZilLuwfhHLOVmNKlT/wr4A9Dna94mgTdz3lTfrdJ72QRx7IHCv/LzoJPmFSfK/N6WA==", "path": "system.drawing.common/9.0.5", "hashPath": "system.drawing.common.9.0.5.nupkg.sha512"}, "System.Memory/4.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-qdcDOgnFZY40+Q9876JUHnlHu7bosOHX8XISRoH94fwk6hgaeQGSgfZd8srWRZNt5bV9ZW2TljcegDNxsf+96A==", "path": "system.memory/4.6.3", "hashPath": "system.memory.4.6.3.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}}}